
.footer {
  background: linear-gradient(135deg,
    rgba(68, 41, 63, 0.95) 0%,
    rgba(94, 65, 79, 0.92) 50%,
    rgba(108, 70, 85, 0.95) 100%);
  color: #e8d4e3;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  margin-top: auto;
  display: block;
  visibility: visible;
  z-index: 10;
}

.footer-container {
  max-width: 1700px; 
  margin: 0 auto;
  padding: 0 10px;
  width: 100%;
  box-sizing: border-box;
}


.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 16px; /* Reduced from 24px */
  padding: 20px 0 16px 0; /* Reduced from 32px 0 24px 0 */
}


.brand-section {
  max-width: 350px;
}

.footer-logo h2 {
  font-family: 'Pacifico', cursive;
  font-size: 1.3rem; /* Reduced from 1.6rem */
  color: #f8bbd0;
  margin: 0 0 6px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-accent {
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #cc4068, #f8bbd0);
  border-radius: 2px;
  margin-bottom: 12px;
}

.footer-description {
  font-family: 'Poppins', sans-serif;
  font-size: 0.75rem; /* Reduced from 0.85rem */
  line-height: 1.5;
  color: #d4a1ba;
  margin-bottom: 12px; /* Reduced from 16px */
}


.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px; /* Reduced from 36px */
  height: 30px; /* Reduced from 36px */
  background: rgba(204, 64, 104, 0.1);
  border: 1.5px solid rgba(204, 64, 104, 0.3);
  border-radius: 10px;
  color: #f8bbd0;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-size: 0.85rem; /* Reduced from 0.95rem */
}

.social-link:hover {
  background: rgba(204, 64, 104, 0.2);
  border-color: rgba(204, 64, 104, 0.5);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(204, 64, 104, 0.3);
}


.footer-section h3.footer-title {
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #f8bbd0;
  margin: 0 0 12px 0;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #cc4068, transparent);
  border-radius: 1px;
}


.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-link {
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  color: #d4a1ba;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-link:hover {
  color: #f8bbd0;
  transform: translateX(4px);
}

.footer-link::before {
  content: '';
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: #cc4068;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.footer-link:hover::before {
  opacity: 1;
}


.contact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 0.8rem;
  color: #d4a1ba;
  line-height: 1.4;
}

.contact-icon {
  color: #cc4068;
  font-size: 1rem;
  margin-top: 2px;
  min-width: 16px;
}




.footer-bottom {
  border-top: 1px solid rgba(204, 64, 104, 0.2);
  padding: 12px 0; /* Reduced from 16px 0 */
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.copyright p {
  font-family: 'Poppins', sans-serif;
  font-size: 0.85rem;
  color: #b8a1b0;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 24px;
}

.footer-bottom-link {
  font-family: 'Poppins', sans-serif;
  font-size: 0.85rem;
  color: #b8a1b0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-bottom-link:hover {
  color: #d4a1ba;
}


@media (max-width: 1024px) {
  .footer-container {
    padding: 0 16px;
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 28px 0 20px 0;
  }

  .brand-section {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 0 16px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 40px 0 30px 0;
    text-align: center;
  }

  .brand-section {
    max-width: none;
  }

  .footer-logo h2 {
    font-size: 2rem;
  }

  .social-links {
    justify-content: center;
  }

  .newsletter-section {
    padding: 24px;
    margin: 0 0 30px 0;
  }

  .newsletter-form {
    flex-direction: column;
    max-width: 300px;
  }

  .newsletter-button {
    justify-content: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .footer-bottom-links {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 0 12px;
  }

  .footer-content {
    gap: 24px;
    padding: 32px 0 24px 0;
  }

  .footer-logo h2 {
    font-size: 1.8rem;
  }

  .footer-description {
    font-size: 0.9rem;
  }

  .footer-title {
    font-size: 1.1rem;
  }

  .footer-link {
    font-size: 0.85rem;
  }

  .contact-item {
    font-size: 0.85rem;
  }

  .social-links {
    gap: 8px;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .newsletter-section {
    padding: 20px;
    border-radius: 12px;
  }

  .newsletter-content h3 {
    font-size: 1.2rem;
  }

  .newsletter-content p {
    font-size: 0.9rem;
  }

  .newsletter-input,
  .newsletter-button {
    padding: 10px 14px;
    font-size: 0.85rem;
  }

  .footer-bottom {
    padding: 20px 0;
  }

  .copyright p,
  .footer-bottom-link {
    font-size: 0.8rem;
  }

  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
  }
}









