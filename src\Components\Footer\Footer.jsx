import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);

  // Handle newsletter subscription
  const handleNewsletterSubmit = async (e) => {
    e.preventDefault();
    if (!email.trim()) {
      alert('Please enter a valid email address');
      return;
    }

    setIsSubscribing(true);
    try {
      // You can implement actual newsletter subscription logic here
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      alert('Thank you for subscribing to our newsletter!');
      setEmail('');
    } catch (error) {
      alert('Failed to subscribe. Please try again.');
    } finally {
      setIsSubscribing(false);
    }
  };

  // Handle social media clicks
  const handleSocialClick = (platform) => {
    // You can implement actual social media links here
    console.log(`Opening ${platform} page`);
    // For now, just show an alert
    alert(`This would open the ${platform} page`);
  };

  // Handle footer link clicks
  const handleFooterLinkClick = (linkType) => {
    console.log(`Opening ${linkType} page`);
    // You can implement actual page navigation here
    alert(`This would open the ${linkType} page`);
  };

  // Handle contact clicks
  const handleContactClick = (type, value) => {
    switch(type) {
      case 'phone':
        window.open(`tel:${value}`);
        break;
      case 'email':
        window.open(`mailto:${value}`);
        break;
      case 'address':
        // Open in Google Maps
        const encodedAddress = encodeURIComponent(value);
        window.open(`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`);
        break;
      default:
        break;
    }
  };

  return (
    <footer className="footer">
      <div className="footer-container">
        {/* Footer content */}
        <div className="footer-content">
          {/* Brand Section */}
          <div className="brand-section">
            <div className="footer-logo">
              <h2>Sippin' Pretty</h2>
              <div className="logo-accent"></div>
            </div>
            <p className="footer-description">
              We're not ordinary. We're made to shine, stand out, and carry ourselves
              with flair and confidence just like a perfectly crafted latte with a designer touch.
            </p>
            <div className="social-links">
              <button
                onClick={() => handleSocialClick('Facebook')}
                className="social-link"
                aria-label="Facebook"
              >
                <i className="fab fa-facebook-f"></i>
              </button>
              <button
                onClick={() => handleSocialClick('Instagram')}
                className="social-link"
                aria-label="Instagram"
              >
                <i className="fab fa-instagram"></i>
              </button>
              <button
                onClick={() => handleSocialClick('Twitter')}
                className="social-link"
                aria-label="Twitter"
              >
                <i className="fab fa-twitter"></i>
              </button>
              <button
                onClick={() => handleSocialClick('TikTok')}
                className="social-link"
                aria-label="TikTok"
              >
                <i className="fab fa-tiktok"></i>
              </button>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h3 className="footer-title">Quick Links</h3>
            <ul className="footer-links">
              <li><Link to="/" className="footer-link">Home</Link></li>
              <li><Link to="/menu" className="footer-link">Menu</Link></li>
              <li><Link to="/booking" className="footer-link">Book a Table</Link></li>
              <li><Link to="/aboutus" className="footer-link">About Us</Link></li>
            </ul>
          </div>

          {/* Services */}
          <div className="footer-section">
            <h3 className="footer-title">Services</h3>
            <ul className="footer-links">
              <li><a href="#" className="footer-link">Dine In</a></li>
              <li><a href="#" className="footer-link">Takeaway</a></li>
              <li><a href="#" className="footer-link">Delivery</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h3 className="footer-title">Contact Us</h3>
            <div className="contact-info">
              <div
                className="contact-item clickable"
                onClick={() => handleContactClick('address', '123 Coffee Street, Mowbray, Cape Town, 7700')}
              >
                <i className="fas fa-map-marker-alt contact-icon"></i>
                <span>123 Coffee Street, Mowbray, Cape Town, 7700</span>
              </div>
              <div
                className="contact-item clickable"
                onClick={() => handleContactClick('phone', '0216941960')}
              >
                <i className="fas fa-phone contact-icon"></i>
                <span>************</span>
              </div>
              <div
                className="contact-item clickable"
                onClick={() => handleContactClick('email', '<EMAIL>')}
              >
                <i className="fas fa-envelope contact-icon"></i>
                <span><EMAIL></span>
              </div>
              <div className="contact-item">
                <i className="fas fa-clock contact-icon"></i>
                <span>Mon-Sun: 9AM - 4PM</span>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter section */}
        <div className="newsletter-section">
          <div className="newsletter-content">
            <h3>Stay Pretty & Informed</h3>
            <p>Subscribe to our newsletter for exclusive offers, new menu items, and coffee tips!</p>
            <form className="newsletter-form" onSubmit={handleNewsletterSubmit}>
              <input
                type="email"
                className="newsletter-input"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
              <button
                type="submit"
                className="newsletter-button"
                disabled={isSubscribing}
              >
                <i className="fas fa-paper-plane"></i>
                {isSubscribing ? 'Subscribing...' : 'Subscribe'}
              </button>
            </form>
          </div>
        </div>

        {/* Footer bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <div className="copyright">
              <p>&copy; {currentYear} Sippin' Pretty. All rights reserved.</p>
            </div>
            <div className="footer-bottom-links">
              <button
                onClick={() => handleFooterLinkClick('Privacy Policy')}
                className="footer-bottom-link"
              >
                Privacy Policy
              </button>
              <button
                onClick={() => handleFooterLinkClick('Terms of Service')}
                className="footer-bottom-link"
              >
                Terms of Service
              </button>
              <button
                onClick={() => handleFooterLinkClick('Cookie Policy')}
                className="footer-bottom-link"
              >
                Cookie Policy
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

