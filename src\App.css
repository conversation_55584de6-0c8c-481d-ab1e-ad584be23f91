@import url('https://fonts.googleapis.com/css2?family=Pacifico&family=Poppins:wght@300;400;600&display=swap');

body, html, #root {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Poppins', sans-serif;
  background: 
    linear-gradient(to right, rgba(202, 153, 160, 0.7), rgba(231, 187, 195, 0.6)),
    url('https://www.transparenttextures.com/patterns/paper-fibers.png');
  background-attachment: fixed;
  background-size: cover;
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
  position: relative;
}

/* Subtle aging texture layer */
body::before {
  content: "";
  position: fixed;
  inset: 0;
  background-image: url('https://www.transparenttextures.com/patterns/white-wall-3.png');
  opacity: 0.25;
  z-index: -1;
  pointer-events: none;
}

/* Optional vignette for old photo feel */
body::after {
  content: "";
  position: fixed;
  inset: 0;
  background: radial-gradient(ellipse at center, rgba(0,0,0,0) 70%, rgba(0,0,0,0.08) 100%);
  z-index: -2;
  pointer-events: none;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

/* Main content should take available space, footer stays at bottom */
.app-container > *:not(footer) {
  flex: 1 0 auto;
}

/* Ensure footer stays at bottom */
.app-container > footer {
  flex-shrink: 0;
  margin-top: auto;
}

@media (min-width: 768px) {
  .app-container {
    padding: 0 2rem 2rem 2rem; /* No top padding to keep navbar at top */
  }
}

.left-heading h1 {
  font-size: 2.5rem;
  color: #5e3b4f;
  text-shadow:
    1px 1px #fff8dc,
    0 0 5px rgba(212, 175, 55, 0.3),
    0 0 15px rgba(212, 175, 55, 0.2);
}

/* Modal */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.modal-content {
  background: #fffaf5;
  border-radius: 15px;
  padding: 2rem;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 8px 20px rgba(0,0,0,0.15);
  text-align: center;
  animation: pop-in 0.3s ease-out;
}

.modal-content h2 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #6d4c41;
}

.modal-content button {
  background: linear-gradient(to right, #f8bbd0, #f9c9d4);
  box-shadow: 0 4px 10px rgba(223, 149, 190, 0.4);
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-content button:hover {
  background: linear-gradient(to right, #f48fb1, #f9a8d4);
  box-shadow: 0 6px 15px rgba(249, 168, 212, 0.6);
}

/* Animations */
@keyframes pop-in {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

.coffee-mug {
  margin-top: 3rem;
  animation: float 3s ease-in-out infinite;
}

/* Responsive styles */
@media (max-width: 480px) {
  .left-heading h1 {
    font-size: 2rem;
  }
}

@media (max-width: 380px) {
  .app-container {
    padding: 0 0.5rem 0.5rem 0.5rem; /* No top padding */
  }
}

@media (max-width: 320px) {
  .app-container {
    padding: 0 0.3rem 0.3rem 0.3rem; /* No top padding */
  }

  .modal-content {
    padding: 1rem;
    width: 95%;
  }

  h1 {
    font-size: 1.5rem;
  }

  .modal-content button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}
